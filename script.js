// Array de tipos de flores amarillas
const flowerTypes = ['sunflower', 'yellow-flower', 'tulip', 'blossom', 'daisy', 'marigold', 'buttercup', 'daffodil'];

// Función para crear una flor
function createFlower() {
    const flower = document.createElement('div');
    flower.className = `flower ${flowerTypes[Math.floor(Math.random() * flowerTypes.length)]}`;
    
    // Posición aleatoria en el eje X
    flower.style.left = Math.random() * 100 + '%';
    
    // Duración aleatoria de la animación
    flower.style.animationDuration = (Math.random() * 4 + 6) + 's';
    
    // Delay aleatorio
    flower.style.animationDelay = Math.random() * 2 + 's';
    
    return flower;
}

// Función para crear efecto de brillo/sparkle
function createSparkle() {
    const sparkle = document.createElement('div');
    sparkle.className = 'sparkle';
    
    sparkle.style.left = Math.random() * 100 + '%';
    sparkle.style.top = Math.random() * 100 + '%';
    sparkle.style.animationDelay = Math.random() * 3 + 's';
    
    return sparkle;
}

// Función para generar flores continuamente
function generateFlowers() {
    const container = document.getElementById('flowerContainer');
    
    // Crear una nueva flor
    const flower = createFlower();
    container.appendChild(flower);
    
    // Remover la flor después de la animación
    setTimeout(() => {
        if (flower.parentNode) {
            flower.parentNode.removeChild(flower);
        }
    }, 10000);
}

// Función para generar sparkles
function generateSparkles() {
    const container = document.getElementById('flowerContainer');
    
    for (let i = 0; i < 3; i++) {
        const sparkle = createSparkle();
        container.appendChild(sparkle);
        
        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 3000);
    }
}

// Función para el efecto del corazón al hacer clic
function heartClickEffect() {
    const heart = document.querySelector('.heart');
    
    heart.addEventListener('click', () => {
        // Crear múltiples flores al hacer clic
        for (let i = 0; i < 20; i++) {
            setTimeout(() => {
                generateFlowers();
            }, i * 50);
        }
        
        // Crear sparkles adicionales
        generateSparkles();
        
        // Efecto visual en el corazón
        heart.style.transform = 'scale(1.5)';
        setTimeout(() => {
            heart.style.transform = 'scale(1)';
        }, 200);
    });
}

// Función para crear flores iniciales
function createInitialFlowers() {
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            generateFlowers();
        }, i * 200);
    }
}

// Inicializar la aplicación
document.addEventListener('DOMContentLoaded', () => {
    // Crear flores iniciales
    createInitialFlowers();

    // Generar flores cada 800ms (más frecuente)
    setInterval(generateFlowers, 800);

    // Generar múltiples flores cada 1.5 segundos
    setInterval(() => {
        for (let i = 0; i < 3; i++) {
            setTimeout(generateFlowers, i * 100);
        }
    }, 1500);

    // Generar sparkles cada 3 segundos
    setInterval(generateSparkles, 3000);

    // Configurar el efecto del corazón
    heartClickEffect();

    // Crear sparkles iniciales
    setTimeout(generateSparkles, 1000);
});

// Efecto adicional: flores que siguen el cursor (opcional)
let mouseX = 0;
let mouseY = 0;

document.addEventListener('mousemove', (e) => {
    mouseX = e.clientX;
    mouseY = e.clientY;
});

// Crear flores ocasionales en la posición del mouse
setInterval(() => {
    if (Math.random() < 0.6) { // 60% de probabilidad (más flores)
        const container = document.getElementById('flowerContainer');
        const flower = createFlower();

        flower.style.left = (mouseX / window.innerWidth * 100) + '%';
        flower.style.animationDuration = '4s';

        container.appendChild(flower);

        setTimeout(() => {
            if (flower.parentNode) {
                flower.parentNode.removeChild(flower);
            }
        }, 4000);
    }
}, 600); // Más frecuente

// Crear lluvia de flores adicional
setInterval(() => {
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            generateFlowers();
        }, i * 200);
    }
}, 3000);
