* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e, #e17055);
    min-height: 100vh;
    font-family: 'Georgia', serif;
    overflow: hidden;
    position: relative;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.title {
    font-size: 3.5rem;
    color: #2d3436;
    text-align: center;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInTitle 2s ease-in-out;
    z-index: 100;
    position: relative;
    font-weight: bold;
    letter-spacing: 2px;
}

@keyframes fadeInTitle {
    0% {
        opacity: 0;
        transform: translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.flower-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.flower {
    position: absolute;
    font-size: 2rem;
    animation: floatFlower 8s infinite ease-in-out;
    opacity: 0;
}

.flower.sunflower::before {
    content: '🌻';
}

.flower.yellow-flower::before {
    content: '🌼';
}

.flower.tulip::before {
    content: '🌻';
}

.flower.blossom::before {
    content: '🌼';
}

.flower.daisy::before {
    content: '🌻';
}

.flower.marigold::before {
    content: '🌼';
}

.flower.buttercup::before {
    content: '🌻';
}

.flower.daffodil::before {
    content: '🌼';
}

@keyframes floatFlower {
    0% {
        opacity: 0;
        transform: translateY(100vh) rotate(0deg) scale(0.5);
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) rotate(360deg) scale(1.2);
    }
}

.heart-container {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
}

.heart {
    font-size: 4rem;
    animation: heartBeat 2s infinite ease-in-out;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.heart:hover {
    transform: scale(1.2);
}

@keyframes heartBeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .title {
        font-size: 2.5rem;
        padding: 0 1rem;
    }
    
    .flower {
        font-size: 1.5rem;
    }
    
    .heart {
        font-size: 3rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2rem;
    }
    
    .flower {
        font-size: 1.2rem;
    }
    
    .heart {
        font-size: 2.5rem;
    }
}

/* Sparkle effect */
.sparkle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #f1c40f;
    border-radius: 50%;
    animation: sparkleAnimation 3s infinite ease-in-out;
    pointer-events: none;
}

@keyframes sparkleAnimation {
    0%, 100% {
        opacity: 0;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
}
